<?php
/**
 * WordPress 额外配置 - 宠物用品电商网站
 * 用于 Headless WordPress + Next.js 架构
 */

// 启用 CORS 支持
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// JWT 认证配置
define('JWT_AUTH_SECRET_KEY', 'pet-store-jwt-secret-key-2025');
define('JWT_AUTH_CORS_ENABLE', true);

// WooCommerce 配置
define('WC_ADMIN_DISABLED', false);
define('WOOCOMMERCE_BLOCKS_PHASE', 3);

// 性能优化
define('WP_CACHE', true);
define('COMPRESS_CSS', true);
define('COMPRESS_SCRIPTS', true);
define('CONCATENATE_SCRIPTS', false);
define('ENFORCE_GZIP', true);

// 安全设置
define('DISALLOW_FILE_EDIT', true);
define('DISALLOW_FILE_MODS', false);
define('AUTOMATIC_UPDATER_DISABLED', true);

// 调试设置 (生产环境请设置为 false)
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
define('SCRIPT_DEBUG', true);

// 内存限制
ini_set('memory_limit', '512M');
ini_set('max_execution_time', 300);

// 上传文件大小限制
ini_set('upload_max_filesize', '100M');
ini_set('post_max_size', '100M');

// Redis 缓存配置
define('WP_REDIS_HOST', 'redis');
define('WP_REDIS_PORT', 6379);
define('WP_REDIS_TIMEOUT', 1);
define('WP_REDIS_READ_TIMEOUT', 1);
define('WP_REDIS_DATABASE', 0);

// 自定义表前缀 (如果需要)
// $table_prefix = 'wp_';

// 多站点配置 (如果需要)
// define('WP_ALLOW_MULTISITE', true);

// API 相关设置
define('REST_API_ENABLED', true);
define('GRAPHQL_DEBUG', true);

// 文件权限
define('FS_CHMOD_DIR', (0755 & ~ umask()));
define('FS_CHMOD_FILE', (0644 & ~ umask()));

// 自动保存间隔 (秒)
define('AUTOSAVE_INTERVAL', 300);

// 修订版本数量限制
define('WP_POST_REVISIONS', 5);

// 垃圾箱自动清理天数
define('EMPTY_TRASH_DAYS', 30);

// 禁用主题和插件编辑器
define('DISALLOW_FILE_EDIT', true);

// 强制 SSL (生产环境)
// define('FORCE_SSL_ADMIN', true);

// 自定义内容目录 (如果需要)
// define('WP_CONTENT_DIR', '/var/www/html/wp-content');
// define('WP_CONTENT_URL', 'http://localhost:8080/wp-content');

// 自定义上传目录
// define('UPLOADS', 'wp-content/uploads');

// 数据库修复模式 (仅在需要时启用)
// define('WP_ALLOW_REPAIR', true);

// 禁用 WordPress 更新检查 (容器环境)
define('WP_AUTO_UPDATE_CORE', false);
add_filter('pre_site_transient_update_core', '__return_null');
add_filter('pre_site_transient_update_plugins', '__return_null');
add_filter('pre_site_transient_update_themes', '__return_null');

// 自定义错误页面
// define('WP_DEBUG_LOG', '/var/log/wordpress/debug.log');

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 语言设置
define('WPLANG', 'zh_CN');

// 自定义 Cookie 域名 (如果需要)
// define('COOKIE_DOMAIN', '.yourdomain.com');

// Session 配置
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // 生产环境设置为 1

// 自定义常量
define('PET_STORE_VERSION', '1.0.0');
define('PET_STORE_API_VERSION', 'v1');

// 钩子：在 WordPress 加载后执行自定义代码
add_action('init', function() {
    // 移除不必要的 WordPress 头部信息
    remove_action('wp_head', 'wp_generator');
    remove_action('wp_head', 'wlwmanifest_link');
    remove_action('wp_head', 'rsd_link');
    
    // 启用 CORS 支持
    add_action('rest_api_init', function() {
        remove_filter('rest_pre_serve_request', 'rest_send_cors_headers');
        add_filter('rest_pre_serve_request', function($value) {
            header('Access-Control-Allow-Origin: *');
            header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
            header('Access-Control-Allow-Credentials: true');
            return $value;
        });
    }, 15);
});

// 自定义 REST API 端点
add_action('rest_api_init', function() {
    register_rest_route('pet-store/v1', '/health', array(
        'methods' => 'GET',
        'callback' => function() {
            return array(
                'status' => 'healthy',
                'timestamp' => current_time('mysql'),
                'version' => PET_STORE_VERSION
            );
        },
        'permission_callback' => '__return_true'
    ));
});

-- 宠物用品电商网站数据库初始化脚本
-- 创建时间: 2025-08-05

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建数据库 (如果不存在)
CREATE DATABASE IF NOT EXISTS `pet_store_db` 
    CHARACTER SET utf8mb4 
    COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `pet_store_db`;

-- 创建用户并授权 (如果不存在)
CREATE USER IF NOT EXISTS 'wordpress'@'%' IDENTIFIED BY 'wordpress_password';
GRANT ALL PRIVILEGES ON `pet_store_db`.* TO 'wordpress'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 设置 MySQL 配置
SET GLOBAL sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';
SET GLOBAL innodb_file_format = 'Barracuda';
SET GLOBAL innodb_file_per_table = ON;
SET GLOBAL innodb_large_prefix = ON;

-- 创建一些基础表结构优化
-- 这些将在 WordPress 安装时被覆盖，但可以预设一些优化

-- 优化设置
SET GLOBAL query_cache_size = 268435456;
SET GLOBAL query_cache_type = ON;
SET GLOBAL query_cache_limit = 1048576;

-- 连接设置
SET GLOBAL max_connections = 200;
SET GLOBAL max_user_connections = 100;

-- InnoDB 设置
SET GLOBAL innodb_buffer_pool_size = 268435456;
SET GLOBAL innodb_log_file_size = 67108864;
SET GLOBAL innodb_flush_log_at_trx_commit = 2;
SET GLOBAL innodb_flush_method = 'O_DIRECT';

-- 创建日志表 (用于调试和监控)
CREATE TABLE IF NOT EXISTS `pet_store_logs` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `level` varchar(20) NOT NULL DEFAULT 'info',
    `message` text NOT NULL,
    `context` longtext,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `level_created_at` (`level`, `created_at`),
    KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建缓存表 (用于应用级缓存)
CREATE TABLE IF NOT EXISTS `pet_store_cache` (
    `cache_key` varchar(255) NOT NULL,
    `cache_value` longtext,
    `expires_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`cache_key`),
    KEY `expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建会话表 (用于用户会话管理)
CREATE TABLE IF NOT EXISTS `pet_store_sessions` (
    `session_id` varchar(255) NOT NULL,
    `user_id` bigint(20) unsigned DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text,
    `payload` longtext NOT NULL,
    `last_activity` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`session_id`),
    KEY `user_id` (`user_id`),
    KEY `last_activity` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入初始日志记录
INSERT INTO `pet_store_logs` (`level`, `message`, `context`) VALUES 
('info', 'Database initialized successfully', '{"version": "1.0.0", "timestamp": "2025-08-05"}');

-- 创建存储过程：清理过期缓存
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CleanExpiredCache()
BEGIN
    DELETE FROM `pet_store_cache` WHERE `expires_at` IS NOT NULL AND `expires_at` < NOW();
    INSERT INTO `pet_store_logs` (`level`, `message`) VALUES ('info', 'Expired cache cleaned');
END //
DELIMITER ;

-- 创建存储过程：清理旧日志
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CleanOldLogs()
BEGIN
    DELETE FROM `pet_store_logs` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 30 DAY);
    INSERT INTO `pet_store_logs` (`level`, `message`) VALUES ('info', 'Old logs cleaned');
END //
DELIMITER ;

-- 创建事件调度器 (如果启用)
-- SET GLOBAL event_scheduler = ON;

-- 创建定时清理事件
-- CREATE EVENT IF NOT EXISTS `clean_expired_cache`
-- ON SCHEDULE EVERY 1 HOUR
-- DO CALL CleanExpiredCache();

-- CREATE EVENT IF NOT EXISTS `clean_old_logs`
-- ON SCHEDULE EVERY 1 DAY
-- DO CALL CleanOldLogs();

-- 显示创建结果
SELECT 'Database initialization completed successfully' as status;

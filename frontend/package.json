{"name": "pet-ecommerce-frontend", "version": "1.0.0", "description": "宠物用品电商网站前端 - Next.js 14", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "clean": "rm -rf .next out dist"}, "dependencies": {"next": "^14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "@next/font": "^14.2.5", "@headlessui/react": "^2.1.2", "@heroicons/react": "^2.1.5", "clsx": "^2.1.1", "tailwind-merge": "^2.4.0", "class-variance-authority": "^0.7.0", "lucide-react": "^0.417.0", "framer-motion": "^11.3.19", "react-hook-form": "^7.52.1", "@hookform/resolvers": "^3.9.0", "zod": "^3.23.8", "axios": "^1.7.2", "swr": "^2.2.5", "react-query": "^3.39.3", "@tanstack/react-query": "^5.51.15", "zustand": "^4.5.4", "immer": "^10.1.1", "react-hot-toast": "^2.4.1", "react-loading-skeleton": "^3.4.0", "react-intersection-observer": "^9.13.0", "swiper": "^11.1.8", "embla-carousel-react": "^8.1.7", "react-image-gallery": "^1.3.0", "react-select": "^5.8.0", "react-datepicker": "^7.3.0", "date-fns": "^3.6.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "sharp": "^0.33.4", "next-themes": "^0.3.0", "react-helmet-async": "^2.0.5", "next-seo": "^6.5.0", "sitemap": "^8.0.0"}, "devDependencies": {"@types/node": "^20.14.12", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.6", "typescript": "^5.5.4", "eslint": "^8.57.0", "eslint-config-next": "^14.2.5", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "prettier": "^3.3.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "tailwindcss": "^3.4.7", "postcss": "^8.4.40", "autoprefixer": "^10.4.19", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.13", "@tailwindcss/aspect-ratio": "^0.4.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^16.0.0", "@testing-library/jest-dom": "^6.4.8", "@testing-library/user-event": "^14.5.2", "cross-env": "^7.0.3", "@next/bundle-analyzer": "^14.2.5", "husky": "^9.1.4", "lint-staged": "^15.2.7"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check"}}}
{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "es6", "ES2022"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/hooks/*": ["./src/hooks/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/styles/*": ["./src/styles/*"], "@/store/*": ["./src/store/*"], "@/api/*": ["./src/api/*"], "@/constants/*": ["./src/constants/*"]}, "types": ["node", "jest", "@testing-library/jest-dom"], "declaration": false, "declarationMap": false, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": true, "allowUnreachableCode": false, "allowUnusedLabels": false}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/**/*", "types/**/*"], "exclude": ["node_modules", ".next", "out", "dist", "build", "coverage", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "ts-node": {"compilerOptions": {"module": "CommonJS"}}}
/** @type {import('next').NextConfig} */
const nextConfig = {
  // 基本配置
  reactStrictMode: true,
  swcMinify: true,
  
  // 实验性功能
  experimental: {
    appDir: true,
    serverComponentsExternalPackages: ['sharp'],
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', '@heroicons/react'],
  },

  // 图片优化配置
  images: {
    domains: [
      'localhost',
      'wordpress',
      'pet-store-wordpress',
      // 添加你的生产域名
    ],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60 * 60 * 24 * 7, // 7 days
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // 环境变量配置
  env: {
    WORDPRESS_API_URL: process.env.WORDPRESS_API_URL || 'http://localhost:8080/wp-json',
    WORDPRESS_GRAPHQL_URL: process.env.WORDPRESS_GRAPHQL_URL || 'http://localhost:8080/graphql',
    NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
  },

  // 重写规则
  async rewrites() {
    return [
      // API 代理到 WordPress
      {
        source: '/api/wp/:path*',
        destination: `${process.env.WORDPRESS_API_URL || 'http://wordpress:80/wp-json'}/:path*`,
      },
      // GraphQL 代理
      {
        source: '/api/graphql',
        destination: `${process.env.WORDPRESS_GRAPHQL_URL || 'http://wordpress:80/graphql'}`,
      },
    ];
  },

  // 重定向规则
  async redirects() {
    return [
      // 旧路径重定向示例
      {
        source: '/shop',
        destination: '/products',
        permanent: true,
      },
    ];
  },

  // 头部配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ];
  },

  // Webpack 配置
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // 优化配置
    config.optimization = {
      ...config.optimization,
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      },
    };

    // 别名配置
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': require('path').resolve(__dirname, './src'),
      '@/components': require('path').resolve(__dirname, './src/components'),
      '@/lib': require('path').resolve(__dirname, './src/lib'),
      '@/hooks': require('path').resolve(__dirname, './src/hooks'),
      '@/types': require('path').resolve(__dirname, './src/types'),
      '@/utils': require('path').resolve(__dirname, './src/utils'),
      '@/styles': require('path').resolve(__dirname, './src/styles'),
    };

    // 生产环境优化
    if (!dev && !isServer) {
      config.resolve.alias = {
        ...config.resolve.alias,
        'react/jsx-runtime.js': 'preact/compat/jsx-runtime',
        react: 'preact/compat',
        'react-dom/test-utils': 'preact/test-utils',
        'react-dom': 'preact/compat',
      };
    }

    return config;
  },

  // 输出配置
  output: 'standalone',
  
  // 压缩配置
  compress: true,
  
  // 电源效率配置
  poweredByHeader: false,
  
  // 生成 sitemap
  generateBuildId: async () => {
    return `build-${Date.now()}`;
  },

  // TypeScript 配置
  typescript: {
    ignoreBuildErrors: false,
  },

  // ESLint 配置
  eslint: {
    ignoreDuringBuilds: false,
  },

  // 静态文件配置
  trailingSlash: false,
  
  // 国际化配置 (如果需要)
  // i18n: {
  //   locales: ['zh-CN', 'en'],
  //   defaultLocale: 'zh-CN',
  // },
};

// Bundle Analyzer 配置
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

module.exports = withBundleAnalyzer(nextConfig);

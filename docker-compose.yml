version: '3.8'

services:
  # Next.js 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: pet-store-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8080/wp-json
      - NEXT_PUBLIC_WORDPRESS_URL=http://wordpress:80
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - wordpress
      - redis
    networks:
      - pet-store-network
    restart: unless-stopped

  # WordPress 后端服务 (Headless CMS)
  wordpress:
    image: wordpress:6.6-php8.3-apache
    container_name: pet-store-wordpress
    environment:
      WORDPRESS_DB_HOST: mysql:3306
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wordpress_password
      WORDPRESS_DB_NAME: pet_store_db
      WORDPRESS_TABLE_PREFIX: wp_
      WORDPRESS_DEBUG: 1
      WORDPRESS_CONFIG_EXTRA: |
        define('WP_ALLOW_MULTISITE', true);
        define('CORS_ALLOW_CREDENTIALS', true);
        define('JWT_AUTH_SECRET_KEY', 'your-secret-key-here');
        define('JWT_AUTH_CORS_ENABLE', true);
    volumes:
      - wordpress_data:/var/www/html
      - ./wordpress/themes:/var/www/html/wp-content/themes
      - ./wordpress/plugins:/var/www/html/wp-content/plugins
      - ./wordpress/uploads:/var/www/html/wp-content/uploads
      - ./wordpress/wp-config-extra.php:/var/www/html/wp-config-extra.php
    depends_on:
      - mysql
    networks:
      - pet-store-network
    restart: unless-stopped

  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    container_name: pet-store-mysql
    environment:
      MYSQL_DATABASE: pet_store_db
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wordpress_password
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - pet-store-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  # Redis 缓存服务
  redis:
    image: redis:7.2-alpine
    container_name: pet-store-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - pet-store-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx 反向代理
  nginx:
    image: nginx:1.25-alpine
    container_name: pet-store-nginx
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - wordpress
    networks:
      - pet-store-network
    restart: unless-stopped

  # phpMyAdmin (开发环境数据库管理)
  phpmyadmin:
    image: phpmyadmin:5.2
    container_name: pet-store-phpmyadmin
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root_password
    ports:
      - "8081:80"
    depends_on:
      - mysql
    networks:
      - pet-store-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  wordpress_data:

networks:
  pet-store-network:
    driver: bridge
